import { _decorator, Component, Node, Button, Label } from 'cc';
import { ItemManager, ItemType } from '../ItemManager';
import { GameData } from '../GameData';
const { ccclass, property } = _decorator;

/**
 * 双倍金币卡UI控制器
 * 负责处理双倍金币卡的购买、使用、显示等UI逻辑
 */
@ccclass('DoubleCoinCard')
export class DoubleCoinCard extends Component {

    @property(Button)
    purchaseButton: Button = null;

    @property(Button)
    useButton: Button = null;

    @property(Button)
    disableButton: Button = null;

    @property(Label)
    priceLabel: Label = null;

    @property(Label)
    numberLabel: Label = null;

    @property(Node)
    insufficientSprite: Node = null;

    @property(Node)
    coinLackSprite: Node = null; // 金币不足提示节点

    start() {
        this.initializeUI();
        this.setupButtonEvents();
        this.updateDisplay();
    }

    onEnable() {
        // 每次节点激活时刷新显示状态
        this.scheduleOnce(() => {
            this.updateDisplay();
        }, 0.1);
    }

    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 设置价格标签
        if (this.priceLabel) {
            const price = ItemManager.getItemPrice(ItemType.DOUBLE_COIN);
            this.priceLabel.string = price.toString();
        }

        // 确保金币不足提示初始隐藏
        if (this.coinLackSprite) {
            this.coinLackSprite.active = false;
        }

        // 确保使用按钮和禁用按钮位置一致（已在编辑器中设置）
        console.log("DoubleCoinCard UI 初始化完成");
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 购买按钮事件
        if (this.purchaseButton) {
            this.purchaseButton.node.on(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        // 使用按钮事件
        if (this.useButton) {
            this.useButton.node.on(Button.EventType.CLICK, this.onUseClick, this);
        }

        // 禁用按钮事件
        if (this.disableButton) {
            this.disableButton.node.on(Button.EventType.CLICK, this.onDisableClick, this);
        }
    }

    /**
     * 购买按钮点击事件
     */
    private onPurchaseClick(): void {
        console.log("点击购买双倍金币卡");

        // 检查金币是否足够
        const totalCoins = GameData.getTotalCoins();
        const price = ItemManager.getItemPrice(ItemType.DOUBLE_COIN);

        if (totalCoins < price) {
            console.log(`金币不足，需要 ${price} 金币，当前只有 ${totalCoins} 金币`);
            this.showCoinLackSprite();
            return;
        }

        const success = ItemManager.purchaseItem(ItemType.DOUBLE_COIN);
        if (success) {
            this.updateDisplay();
            console.log("购买成功！");
        } else {
            console.log("购买失败");
            this.showCoinLackSprite();
        }
    }

    /**
     * 使用按钮点击事件
     */
    private onUseClick(): void {
        console.log("点击使用双倍金币卡");

        const currentCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        if (currentCount <= 0) {
            console.log("双倍金币卡数量不足");
            this.showInsufficientSprite();
            return;
        }

        // 只激活效果，不减少数量
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);
        this.updateDisplay();
        console.log("双倍金币卡已激活！");
    }

    /**
     * 禁用按钮点击事件
     */
    private onDisableClick(): void {
        console.log("点击禁用双倍金币卡");

        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        this.updateDisplay();
        console.log("双倍金币卡已禁用");
    }

    /**
     * 更新显示
     */
    public updateDisplay(): void {
        // 更新数量标签
        if (this.numberLabel) {
            const count = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
            this.numberLabel.string = count.toString();
        }

        // 更新按钮状态
        const isActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);

        // 使用按钮：未激活时显示（不管是否有道具）
        if (this.useButton) {
            this.useButton.node.active = !isActive;
        }

        // 禁用按钮：已激活时显示
        if (this.disableButton) {
            this.disableButton.node.active = isActive;
        }

        // 购买按钮：始终显示且可交互
        if (this.purchaseButton) {
            this.purchaseButton.node.active = true;
            // 始终保持按钮可交互，金币不足时在点击事件中处理
            this.purchaseButton.interactable = true;
        }

        console.log(`DoubleCoinCard 显示更新: 数量=${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}, 激活=${isActive}`);
    }

    /**
     * 显示数量不足提示
     */
    private showInsufficientSprite(): void {
        if (this.insufficientSprite) {
            this.insufficientSprite.active = true;
            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.insufficientSprite) {
                    this.insufficientSprite.active = false;
                }
            }, 2.0);
        }
    }

    /**
     * 显示金币不足提示
     */
    private showCoinLackSprite(): void {
        if (this.coinLackSprite) {
            this.coinLackSprite.active = true;
            console.log("显示金币不足提示");

            // 2秒后自动隐藏
            this.scheduleOnce(() => {
                if (this.coinLackSprite && this.coinLackSprite.isValid) {
                    this.coinLackSprite.active = false;
                    console.log("隐藏金币不足提示");
                }
            }, 2.0);
        } else {
            console.error("金币不足提示节点未设置！");
        }
    }

    /**
     * 外部调用更新显示（比如从其他UI返回时）
     */
    public refreshDisplay(): void {
        this.updateDisplay();
    }

    onDestroy() {
        // 移除事件监听
        if (this.purchaseButton && this.purchaseButton.node && this.purchaseButton.node.isValid) {
            this.purchaseButton.node.off(Button.EventType.CLICK, this.onPurchaseClick, this);
        }

        if (this.useButton && this.useButton.node && this.useButton.node.isValid) {
            this.useButton.node.off(Button.EventType.CLICK, this.onUseClick, this);
        }

        if (this.disableButton && this.disableButton.node && this.disableButton.node.isValid) {
            this.disableButton.node.off(Button.EventType.CLICK, this.onDisableClick, this);
        }
    }
}
