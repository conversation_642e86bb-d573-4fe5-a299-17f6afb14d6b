import { _decorator, AudioClip, Component, Label, Node, RigidBody2D, Vec2 } from 'cc';
import { <PERSON> } from './Bird';
import { PipeSpawner } from './PipeSpawner';
import { CoinSpawner } from './CoinSpawner';
import { GameReadyUI } from './UI/GameReadyUI';
import { GameData, GameMode } from './GameData';
import { GameOverUI } from './UI/GameOverUI';
import { AudioMgr } from './AudioMgr';
import { CoinLabel } from './UI/CoinLabel';
import { EnergyManager } from './EnergyManager';
import { EnergyExhaustedPanel } from './EnergyExhaustedPanel';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { FogEffect } from './FogEffect';
import { SnowSpawner } from './SnowSpawner';
import { WindChallengeManager } from './WindChallengeManager';
import { BackgroundManager } from './BackgroundManager';
import { ItemManager } from './ItemManager';
const { ccclass, property } = _decorator;


enum GameState{
    Ready,
    Gaming,
    GameOver
}

@ccclass('GameManager')
export class GameManager extends Component {

    private static _inst:GameManager = null;
    public static inst(){
        return this._inst;
    }

    @property
    moveSpeed:number = 200; // 基础移动速度（轻松难度）

    @property
    reviveBirdXOffset:number = 150; // 复活时小鸟X轴偏移量

    // 不同难度的速度倍率
    private readonly SPEED_MULTIPLIER_EASY: number = 1.0;
    private readonly SPEED_MULTIPLIER_NORMAL: number = 1.2;
    private readonly SPEED_MULTIPLIER_HARD: number = 1.5;

    // 不同难度的重力倍率（基于轻松难度2.5g）
    private readonly GRAVITY_VALUE_EASY: number = 2.5;
    private readonly GRAVITY_VALUE_NORMAL: number = 3.3;  // 新的标准难度重力值
    private readonly GRAVITY_VALUE_HARD: number = 4.0;    // 新的困难难度重力值

    // 获取当前难度下的移动速度
    public getCurrentMoveSpeed(): number {
        const difficulty = GameDifficulty.getDifficulty();

        switch(difficulty) {
            case GameDifficulty.DIFFICULTY_EASY:
                return this.moveSpeed * this.SPEED_MULTIPLIER_EASY;
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.moveSpeed * this.SPEED_MULTIPLIER_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.moveSpeed * this.SPEED_MULTIPLIER_HARD;
            default:
                return this.moveSpeed;
        }
    }

    // 获取当前难度下的重力值
    public getCurrentGravityValue(): number {
        const difficulty = GameDifficulty.getDifficulty();

        switch(difficulty) {
            case GameDifficulty.DIFFICULTY_EASY:
                return this.GRAVITY_VALUE_EASY;
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.GRAVITY_VALUE_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.GRAVITY_VALUE_HARD;
            default:
                return this.GRAVITY_VALUE_EASY;
        }
    }

    @property('Bird')
    bird:Bird = null;

    // 背景管理器
    @property('BackgroundManager')
    backgroundManager:BackgroundManager = null;

    // 保留原有的引用作为兼容（将通过backgroundManager动态获取）
    @property('MoveBg')
    bgMoving:any = null;
    @property('MoveBg')
    landMoving:any = null;
    @property(PipeSpawner)
    pipeSpawner:PipeSpawner = null;
    @property(CoinSpawner)
    coinSpawner:CoinSpawner = null;
    @property(GameReadyUI)
    gameReadyUI:GameReadyUI = null;
    @property(Node)
    gamingUI:Node = null;
    @property(GameOverUI)
    gameOverUI:GameOverUI = null;

    @property(EnergyExhaustedPanel)
    energyExhaustedPanel:EnergyExhaustedPanel = null;

    // 挑战模式相关组件
    @property(Node)
    fogEffect:Node = null; // 大雾效果节点

    @property(Node)
    snowSpawner:Node = null; // 雪花生成器节点

    @property(WindChallengeManager)
    windChallengeManager:WindChallengeManager = null; // 大风吹挑战管理器

    @property(Label)
    scoreLabel:Label = null;
    @property(CoinLabel)
    coinLabel:CoinLabel = null;
    @property(AudioClip)
    bgAudio:AudioClip = null;
    @property(AudioClip)
    gameOverAudio:AudioClip = null;

    curGS:GameState = GameState.Ready;

    onLoad() {
        GameManager._inst=this;
    }

    protected start(): void {
        // 设置当前游戏模式
        this.setCurrentGameMode();

        // 延迟一帧执行，确保BackgroundManager先完成初始化
        this.scheduleOnce(() => {
            this.transitionToReadyState();
        }, 0);

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            console.log("GameManager: 播放游戏场景BGM");
            AudioMgr.inst.play(this.bgAudio, 0.1);
        } else {
            console.error("GameManager: bgAudio未设置！请在编辑器中将bgm_new.mp3拖拽到GameManager组件的bgAudio属性中");
        }

        // 确保EnergyManager在游戏场景中也能更新
        this.scheduleEnergyUpdate();

        // 处理挑战模式
        this.handleChallengeMode();
    }

    /**
     * 设置当前游戏模式
     */
    private setCurrentGameMode(): void {
        const difficulty = GameDifficulty.getDifficulty();
        const challengeMode = ChallengeMode.getMode();

        const gameMode = GameData.determineGameMode(difficulty, challengeMode);
        GameData.setCurrentGameMode(gameMode);

        console.log(`GameManager: 当前游戏模式 - ${GameData.getGameModeName(gameMode)}`);
        console.log(`难度: ${difficulty}, 挑战模式: ${challengeMode}`);
    }

    /**
     * 处理挑战模式
     */
    private handleChallengeMode(): void {
        // 获取当前挑战模式
        const currentMode = ChallengeMode.getMode();
        console.log(`当前挑战模式: ${currentMode}`);

        // 处理大风吹模式
        if (currentMode === ChallengeModeType.WIND) {
            console.log("大风吹模式已激活");
            this.activateWindMode();
        }

        // 处理大雾模式
        if (currentMode === ChallengeModeType.FOG) {
            console.log("大雾模式已激活");
            this.activateFogMode();
        }

        // 处理大雪模式
        if (currentMode === ChallengeModeType.SNOW) {
            console.log("大雪模式已激活");
            this.activateSnowMode();
        }
    }

    /**
     * 激活大风吹模式
     */
    private activateWindMode(): void {
        // 检查当前模式是否为大风吹模式
        if (ChallengeMode.getMode() !== ChallengeModeType.WIND) {
            console.log("非大风吹模式，不激活大风吹功能");
            return;
        }

        // 检查是否有大风吹挑战管理器
        if (this.windChallengeManager) {
            console.log("大风吹模式：激活大风吹挑战管理器");
            // 大风吹管理器会在自己的start方法中初始化
        } else {
            console.error("未找到大风吹挑战管理器，请在编辑器中设置");
        }
    }

    /**
     * 激活大雾模式
     */
    private activateFogMode(): void {
        // 再次检查当前模式是否为大雾模式，防止其他模式也显示雾效果
        if (ChallengeMode.getMode() !== ChallengeModeType.FOG) {
            console.log("非大雾模式，不显示雾效果");
            // 如果不是大雾模式，确保雾效果节点处于隐藏状态
            if (this.fogEffect) {
                this.fogEffect.active = false;
            }
            return;
        }

        // 检查是否有雾效果节点
        if (this.fogEffect) {
            console.log("大雾模式：显示大雾效果");
            // 激活雾效果节点
            this.fogEffect.active = true;

            // 如果节点上有FogEffect组件，调用其显示方法
            const fogEffectComp = this.fogEffect.getComponent(FogEffect);
            if (fogEffectComp) {
                fogEffectComp.showFogEffect(); // 直接调用显示方法，而不是checkFogMode
            }
        } else {
            console.error("未找到雾效果节点，请在编辑器中设置");
        }
    }

    /**
     * 激活大雪模式
     */
    private activateSnowMode(): void {
        // 再次检查当前模式是否为大雪模式
        if (ChallengeMode.getMode() !== ChallengeModeType.SNOW) {
            console.log("非大雪模式，不显示雪效果");
            // 如果不是大雪模式，确保雪花生成器处于隐藏状态
            if (this.snowSpawner) {
                this.snowSpawner.active = false;
            }
            return;
        }

        // 检查是否有雪花生成器节点
        if (this.snowSpawner) {
            console.log("大雪模式：激活雪花生成器");
            // 激活雪花生成器节点
            this.snowSpawner.active = true;
        } else {
            console.error("未找到雪花生成器节点，请在编辑器中设置");
        }
    }

    public transitionToReadyState(){
        this.curGS = GameState.Ready;

        // 检查是否是复活状态
        if (GameData.isRevived()) {
            console.log("检测到复活状态，恢复游戏数据");
            // 应用复活状态，恢复分数和金币
            GameData.applyReviveState();
            // 更新UI显示
            this.updateUIAfterRevive();

            // 复活后重新播放BGM
            if (this.bgAudio) {
                console.log("复活后重新播放游戏场景BGM");
                AudioMgr.inst.play(this.bgAudio, 0.1);
            }
        } else {
            // 正常游戏开始，重置本局收集的金币数和复活次数
            GameData.resetSessionCoins();
            GameData.resetSessionReviveCount();
        }

        // 重置体力消耗标志，允许下一局游戏消耗体力
        this._energyConsumed = false;

        // 重置道具效果（检查是否有激活的道具）
        ItemManager.resetAllEffects();
        console.log("已重置道具效果");

        this.bird.disableControl();

        // 重置小鸟位置和状态
        this.resetBirdState();

        // 清理场景中的管道和金币（复活时）
        if (GameData.isRevived()) {
            this.clearSceneObjects();
        }

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.disableMoving();
        if (currentLandMoving) currentLandMoving.disableMoving();
        if (currentPipeSpawner) currentPipeSpawner.pause();
        if (this.coinSpawner) {
            this.coinSpawner.pause();
        }

        // 暂停雪花生成器
        if (this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                snowSpawnerComp.pause();
            }
        }

        // 重置大风吹模式状态
        if (this.windChallengeManager && ChallengeMode.getMode() === ChallengeModeType.WIND) {
            this.windChallengeManager.reset();
        }

        // 重置距离控制状态（非大风吹模式）
        if (ChallengeMode.getMode() !== ChallengeModeType.WIND) {
            if (currentPipeSpawner && (currentPipeSpawner as any).resetDistanceTracking) {
                (currentPipeSpawner as any).resetDistanceTracking();
            }
            if (this.coinSpawner && (this.coinSpawner as any).resetDistanceTracking) {
                (this.coinSpawner as any).resetDistanceTracking();
            }
        }

        this.gamingUI.active=false;
        this.gameOverUI.hide();
        this.gameReadyUI.node.active=true;
    }
    // 防止重复消耗体力的标志
    private _energyConsumed: boolean = false;

    transitionToGamingState(){
        // 防止重复进入Gaming状态
        if (this.curGS == GameState.Gaming) {
            console.log("已经在Gaming状态，忽略重复切换");
            return;
        }

        console.log("切换到Gaming状态");

        // 只在本次游戏中第一次调用时消耗体力（复活状态下不消耗体力）
        if (!this._energyConsumed && !GameData.isRevived()) {
            // 尝试消耗体力值
            const energyManager = EnergyManager.getInstance();

            if (energyManager) {
                // 尝试消耗体力值
                const success = energyManager.consumeEnergy();
                if (!success) {
                    console.log("体力不足，无法开始游戏");
                    // 显示体力不足提示面板
                    if (this.energyExhaustedPanel) {
                        this.energyExhaustedPanel.show();
                    }
                    return;
                }
                console.log("成功消耗体力，开始游戏");
                // 标记已消耗体力，防止重复消耗
                this._energyConsumed = true;
            } else {
                console.warn("未找到EnergyManager实例，跳过体力检查");
            }
        } else if (GameData.isRevived()) {
            console.log("复活状态下开始游戏，不消耗体力");
            // 清除复活状态，避免下次游戏还是复活状态
            GameData.clearReviveState();
        } else {
            console.log("本局游戏已消耗过体力，不再重复消耗");
        }

        this.curGS = GameState.Gaming;

        this.bird.enableControl();

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.enableMoving();
        if (currentLandMoving) currentLandMoving.enableMoving();

        // 根据是否为大风吹模式决定启动方式
        if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
            // 大风吹模式：使用WindChallengeManager的距离控制
            console.log("GameManager: 大风吹模式 - 使用WindChallengeManager距离控制生成");
            if (currentPipeSpawner) currentPipeSpawner.startWindMode();
            if (this.coinSpawner) {
                (this.coinSpawner as any).startWindMode();
            }
        } else {
            // 其他模式：使用内置距离控制，确保复活后金币生成位置正确
            console.log("GameManager: 非大风吹模式 - 使用内置距离控制生成");
            if (currentPipeSpawner) {
                (currentPipeSpawner as any).startDistanceMode();
            }
            if (this.coinSpawner) {
                console.log("GameManager: 启动金币生成器（距离控制）");
                (this.coinSpawner as any).startDistanceMode();
            } else {
                console.error("GameManager错误: coinSpawner未设置!");
            }
        }

        // 启动雪花生成器（如果是大雪模式）
        if (ChallengeMode.getMode() === ChallengeModeType.SNOW && this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                console.log("GameManager: 启动雪花生成器");
                snowSpawnerComp.startSpawning();
            }
        }

        this.gameReadyUI.node.active=false;
        this.gamingUI.active=true;
    }
    transitionToGameOverState(){
        if(this.curGS==GameState.GameOver)return;
        this.curGS = GameState.GameOver;

        this.bird.disableControlNotRGD();

        // 通过背景管理器获取当前激活的组件
        const currentBgMoving = this.getCurrentBgMoving();
        const currentLandMoving = this.getCurrentLandMoving();
        const currentPipeSpawner = this.getCurrentPipeSpawner();

        if (currentBgMoving) currentBgMoving.disableMoving();
        if (currentLandMoving) currentLandMoving.disableMoving();
        if (currentPipeSpawner) currentPipeSpawner.pause();
        if (this.coinSpawner) {
            this.coinSpawner.pause();
        }

        // 暂停雪花生成器
        if (this.snowSpawner) {
            const snowSpawnerComp = this.snowSpawner.getComponent(SnowSpawner);
            if (snowSpawnerComp) {
                snowSpawnerComp.pause();
            }
        }

        this.gamingUI.active=false;

        // 获取当前模式的最高分
        const currentMode = GameData.getCurrentGameMode();
        const currentModeBestScore = GameData.getBestScore(currentMode);
        this.gameOverUI.show( GameData.getScore() , currentModeBestScore);
        // 注意：saveScore 已移至 GameOverUI.show 方法中，避免重复调用
        AudioMgr.inst.stop();
        AudioMgr.inst.playOneShot(this.gameOverAudio);

        // 输出本局收集的金币数
        console.log(`游戏结束! 本局收集金币: ${GameData.getSessionCoins()}, 总金币: ${GameData.getTotalCoins()}`);
    }

    addScore(count:number=1){
        GameData.addScore(count);
        this.scoreLabel.string = GameData.getScore().toString();
    }

    /**
     * 更新金币显示
     * @param count 增加的金币数量，默认为1
     */
    addCoin(count:number=1){
        GameData.addCoin(count);
        // 更新金币显示
        if (this.coinLabel) {
            this.coinLabel.updateDisplay();
        }
    }

    /**
     * 为体力系统添加额外的更新检查
     */
    private scheduleEnergyUpdate() {
        // 每秒检查体力恢复
        this.schedule(this.checkEnergyRecovery, 1.0);
    }

    /**
     * 检查体力恢复，确保在游戏场景中体力恢复计时器能正常工作
     */
    private checkEnergyRecovery() {
        const energyManager = EnergyManager.getInstance();
        if (!energyManager) return;

        // 主动触发体力检查
        if (energyManager.getTimeUntilNextRecover() <= 0 &&
            energyManager.getCurrentEnergy() < energyManager.getMaxEnergy()) {
            console.log("GameManager: 强制检查体力恢复");
            energyManager.forceCheckEnergyRecover();
        }
    }

    update(deltaTime: number) {
        // 在大风吹模式下更新距离
        if (this.windChallengeManager && ChallengeMode.getMode() === ChallengeModeType.WIND && this.curGS === GameState.Gaming) {
            this.windChallengeManager.updateDistance(deltaTime);
        }
    }

    onDestroy() {
        // 取消体力更新检查
        this.unschedule(this.checkEnergyRecovery);
    }

    /**
     * 获取当前激活的背景移动组件
     */
    private getCurrentBgMoving(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentBgMoving();
        }
        return this.bgMoving; // 兼容模式
    }

    /**
     * 获取当前激活的地面移动组件
     */
    private getCurrentLandMoving(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentLandMoving();
        }
        return this.landMoving; // 兼容模式
    }

    /**
     * 获取当前激活的管道生成器组件
     */
    private getCurrentPipeSpawner(): any {
        if (this.backgroundManager) {
            return this.backgroundManager.getCurrentPipeSpawner();
        }
        return this.pipeSpawner; // 兼容模式
    }

    /**
     * 切换背景套装
     * @param setNumber 背景套装编号 (1, 2, 3)
     */
    public switchBackgroundSet(setNumber: number): void {
        if (this.backgroundManager) {
            console.log(`GameManager: 切换到背景套装 ${setNumber}`);
            this.backgroundManager.switchToBackgroundSet(setNumber);

            // 如果游戏正在进行中，重启当前背景的组件
            if (this.curGS === 1) { // GameState.Gaming = 1
                this.restartCurrentBackgroundComponents();
            }
        } else {
            console.warn("BackgroundManager未设置，无法切换背景");
        }
    }

    /**
     * 重启当前背景的组件
     */
    private restartCurrentBackgroundComponents(): void {
        console.log("GameManager: 重启当前背景组件");

        // 延迟一帧执行，确保节点激活状态已更新
        this.scheduleOnce(() => {
            const currentBgMoving = this.getCurrentBgMoving();
            const currentLandMoving = this.getCurrentLandMoving();
            const currentPipeSpawner = this.getCurrentPipeSpawner();

            console.log("当前激活的组件:", {
                bgMoving: !!currentBgMoving,
                landMoving: !!currentLandMoving,
                pipeSpawner: !!currentPipeSpawner
            });

            if (currentBgMoving) {
                currentBgMoving.enableMoving();
                console.log("重启背景移动");
            }

            if (currentLandMoving) {
                currentLandMoving.enableMoving();
                console.log("重启地面移动");
            }

            if (currentPipeSpawner) {
                // 根据挑战模式启动管道生成器
                if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
                    currentPipeSpawner.startWindMode();
                    console.log("重启管道生成器（大风吹模式）");
                } else {
                    (currentPipeSpawner as any).startDistanceMode();
                    console.log("重启管道生成器（距离控制模式）");
                }
            }

            // 重启金币生成器
            if (this.coinSpawner) {
                if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
                    (this.coinSpawner as any).startWindMode();
                    console.log("重启金币生成器（大风吹模式）");
                } else {
                    (this.coinSpawner as any).startDistanceMode();
                    console.log("重启金币生成器（距离控制模式）");
                }
            }
        }, 0);
    }

    /**
     * 获取当前游戏状态（供BackgroundManager使用）
     */
    public getCurrentGameState(): number {
        return this.curGS;
    }

    /**
     * 重置小鸟状态和位置
     */
    private resetBirdState(): void {
        if (!this.bird || !this.bird.node) {
            console.error("小鸟节点未找到，无法重置状态");
            return;
        }

        // 检查是否是复活状态，调整小鸟位置
        let birdX = -200; // 默认位置
        if (GameData.isRevived()) {
            // 复活状态下，小鸟位置向右偏移
            birdX = -200 + this.reviveBirdXOffset;
            console.log(`复活状态：小鸟位置向右偏移${this.reviveBirdXOffset}`);
        }

        // 重置小鸟位置到初始位置
        this.bird.node.setPosition(birdX, 0, 0);

        // 重置小鸟角度
        this.bird.node.angle = 0;

        // 重置小鸟的物理状态
        const rigidBody = this.bird.getComponent(RigidBody2D);
        if (rigidBody) {
            // 重置速度
            rigidBody.linearVelocity = new Vec2(0, 0);
            rigidBody.angularVelocity = 0;
        }

        console.log(`小鸟状态已重置: 位置=(${birdX}, 0), 角度=0, 速度=(0, 0)`);
    }

    /**
     * 清理场景中的管道和金币（复活时使用）
     */
    private clearSceneObjects(): void {
        console.log("清理场景中的管道和金币");

        let pipeCount = 0;
        let coinCount = 0;
        let snowFlakeCount = 0;

        // 清理当前激活的管道生成器中的所有管道
        const currentPipeSpawner = this.getCurrentPipeSpawner();
        if (currentPipeSpawner && currentPipeSpawner.node) {
            const pipeNodes = currentPipeSpawner.node.children.slice(); // 创建副本避免遍历时修改数组
            pipeNodes.forEach(pipeNode => {
                if (pipeNode && pipeNode.isValid) {
                    pipeNode.destroy();
                    pipeCount++;
                }
            });
        }

        // 清理所有背景管理器中的管道（如果有多个PipeSpawner）
        if (this.backgroundManager) {
            // 清理所有背景套装中的管道
            const pipeSpawners = [
                this.backgroundManager.pipeSpawnSet1,
                this.backgroundManager.pipeSpawnSet2,
                this.backgroundManager.pipeSpawnSet3
            ];

            pipeSpawners.forEach(spawner => {
                if (spawner && spawner.isValid) {
                    const pipeNodes = spawner.children.slice();
                    pipeNodes.forEach(pipeNode => {
                        if (pipeNode && pipeNode.isValid) {
                            pipeNode.destroy();
                            pipeCount++;
                        }
                    });
                }
            });
        }

        // 清理金币生成器中的所有金币
        if (this.coinSpawner && this.coinSpawner.node) {
            const coinNodes = this.coinSpawner.node.children.slice();
            coinNodes.forEach(coinNode => {
                if (coinNode && coinNode.isValid) {
                    coinNode.destroy();
                    coinCount++;
                }
            });
        }

        // 清理雪花生成器中的所有雪花
        if (this.snowSpawner) {
            const snowNodes = this.snowSpawner.children.slice();
            snowNodes.forEach(snowNode => {
                if (snowNode && snowNode.isValid) {
                    snowNode.destroy();
                    snowFlakeCount++;
                }
            });
        }

        console.log(`清理完成: 管道=${pipeCount}个, 金币=${coinCount}个, 雪花=${snowFlakeCount}个`);
    }

    /**
     * 复活后更新UI显示
     */
    private updateUIAfterRevive(): void {
        // 更新分数显示
        if (this.scoreLabel) {
            this.scoreLabel.string = GameData.getScore().toString();
        }

        // 更新金币显示
        if (this.coinLabel) {
            this.coinLabel.updateDisplay();
        }

        console.log(`复活后UI更新完成: 分数=${GameData.getScore()}, 本局金币=${GameData.getSessionCoins()}`);
    }

}


